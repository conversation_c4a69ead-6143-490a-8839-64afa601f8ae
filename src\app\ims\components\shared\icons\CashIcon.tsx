"use client";

import React from 'react';
import Icon from '@ant-design/icons';
import type { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';

/**
 * 自定義現金/金錢圖標組件
 * 
 * 遵循 Antd 設計原則，與其他圖標保持一致的視覺風格。
 * 專為 FastERP 價格相關組件設計，支援多種尺寸和上下文使用。
 * 
 * 特性：
 * - 遵循 Antd 圖標設計規範
 * - 支援所有 Antd Icon 屬性
 * - 響應式設計，適配不同尺寸
 * - 語義化設計，專為金錢/價格場景優化
 * - 支援主題色彩和自定義樣式
 * 
 * 使用方式：
 * ```tsx
 * import { CashIcon } from '@/app/ims/components/shared/icons/CashIcon';
 * 
 * // 基本使用
 * <CashIcon />
 * 
 * // 自定義樣式
 * <CashIcon style={{ color: '#52c41a', fontSize: '16px' }} />
 * 
 * // 在 Tag 中使用
 * <Tag icon={<CashIcon />}>價格</Tag>
 * ```
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// SVG 路徑定義 - 現金/紙鈔圖標
const CashSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* 外框紙鈔 */}
    <rect
      x="2"
      y="6"
      width="20"
      height="12"
      rx="2"
      ry="2"
      fill="none"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    
    {/* 內部裝飾線條 */}
    <rect
      x="4"
      y="8"
      width="16"
      height="8"
      rx="1"
      ry="1"
      fill="none"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
      strokeLinejoin="round"
      opacity="0.6"
    />
    
    {/* 中央圓形（代表面額標記） */}
    <circle
      cx="12"
      cy="12"
      r="2.5"
      fill="none"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    
    {/* 中央金額符號 */}
    <text
      x="12"
      y="12.5"
      textAnchor="middle"
      fontSize="6"
      fontWeight="bold"
      fill="currentColor"
    >
      $
    </text>
    
    {/* 左側裝飾圓點 */}
    <circle
      cx="6.5"
      cy="10"
      r="0.8"
      fill="currentColor"
      opacity="0.7"
    />
    <circle
      cx="6.5"
      cy="14"
      r="0.8"
      fill="currentColor"
      opacity="0.7"
    />
    
    {/* 右側裝飾圓點 */}
    <circle
      cx="17.5"
      cy="10"
      r="0.8"
      fill="currentColor"
      opacity="0.7"
    />
    <circle
      cx="17.5"
      cy="14"
      r="0.8"
      fill="currentColor"
      opacity="0.7"
    />
  </svg>
);

// 硬幣圖標變體
const CoinSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* 外圓 */}
    <circle
      cx="12"
      cy="12"
      r="9"
      fill="none"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    
    {/* 內圓 */}
    <circle
      cx="12"
      cy="12"
      r="6"
      fill="none"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
      strokeLinejoin="round"
      opacity="0.6"
    />
    
    {/* 中央金額符號 */}
    <text
      x="12"
      y="13"
      textAnchor="middle"
      fontSize="8"
      fontWeight="bold"
      fill="currentColor"
    >
      ¥
    </text>
    
    {/* 裝飾線條 */}
    <path
      d="M8 8 L16 16 M16 8 L8 16"
      stroke="currentColor"
      strokeWidth="0.5"
      opacity="0.3"
    />
  </svg>
);

// 錢包圖標變體
const WalletSvg = () => (
  <svg
    width="1em"
    height="1em"
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    {/* 錢包主體 */}
    <path
      d="M4 8 C4 6.895 4.895 6 6 6 L18 6 C19.105 6 20 6.895 20 8 L20 16 C20 17.105 19.105 18 18 18 L6 18 C4.895 18 4 17.105 4 16 L4 8 Z"
      fill="none"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    
    {/* 錢包開口 */}
    <path
      d="M4 8 C4 6.895 4.895 6 6 6 L16 6 C17.105 6 18 6.895 18 8"
      fill="none"
      stroke="currentColor"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    
    {/* 卡片插槽 */}
    <rect
      x="16"
      y="10"
      width="3"
      height="4"
      rx="0.5"
      ry="0.5"
      fill="none"
      stroke="currentColor"
      strokeWidth="1"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    
    {/* 錢包內部裝飾 */}
    <circle
      cx="17.5"
      cy="12"
      r="0.5"
      fill="currentColor"
    />
  </svg>
);

// 主要現金圖標組件
export const CashIcon: React.FC<Partial<CustomIconComponentProps>> = (props) => (
  <Icon component={CashSvg} {...props} />
);

// 硬幣圖標組件
export const CoinIcon: React.FC<Partial<CustomIconComponentProps>> = (props) => (
  <Icon component={CoinSvg} {...props} />
);

// 錢包圖標組件
export const WalletIcon: React.FC<Partial<CustomIconComponentProps>> = (props) => (
  <Icon component={WalletSvg} {...props} />
);

// 默認導出現金圖標
export default CashIcon;

// 圖標集合，方便統一管理
export const MoneyIcons = {
  Cash: CashIcon,
  Coin: CoinIcon,
  Wallet: WalletIcon
} as const;

// 類型定義
export type MoneyIconType = keyof typeof MoneyIcons;
