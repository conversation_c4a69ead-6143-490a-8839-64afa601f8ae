"use client";

import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, InputNumber, Button, Switch, Select, Tag, Tooltip, Row, Col, Card, TreeSelect, message } from 'antd';
import {
  InboxOutlined, SettingOutlined, ApartmentOutlined, MoneyCollectOutlined,
  PlusOutlined, FileTextOutlined
} from '@ant-design/icons';
import { Item, ItemTaxTypeOption, PriceType } from '@/services/ims/ItemService';
import { ItemCategory } from '@/services/ims/ItemCategoryService';
import ItemCategoryAdapter from './shared/ItemCategoryAdapter';

interface ItemFormModalProps {
  visible: boolean;
  onClose: () => void;
  selectedItem: Item | null;
  onSubmit: (values: any) => Promise<void>;
  loading: boolean;
  categories: ItemCategory[];
  categoryTreeData: any[];
  sortedCategoriesForDisplay: any[];
  priceTypes: PriceType[];
  itemTaxTypes: ItemTaxTypeOption[];
  itemPrices: Record<string, number>;
  onItemPricesChange: (prices: Record<string, number>) => void;
  onCategoryDataChange: () => void;
  onPriceTypeModalOpen: () => void;
}

const ItemFormModal: React.FC<ItemFormModalProps> = ({
  visible,
  onClose,
  selectedItem,
  onSubmit,
  loading,
  categories,
  categoryTreeData,
  sortedCategoriesForDisplay,
  priceTypes,
  itemTaxTypes,
  itemPrices,
  onItemPricesChange,
  onCategoryDataChange,
  onPriceTypeModalOpen
}) => {
  const [form] = Form.useForm();
  const [isMobile, setIsMobile] = useState(false);
  const [isCategoryModalVisible, setIsCategoryModalVisible] = useState(false);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 當互動視窗打開時設置表單值
  useEffect(() => {
    if (visible && selectedItem) {
      form.setFieldsValue({
        name: selectedItem.name || '',
        customNO: selectedItem.customNO || '',
        internationalBarCode: selectedItem.internationalBarCode || '',
        unit: selectedItem.unit || '',
        itemCategoryID: selectedItem.itemCategoryID || null,
        description: selectedItem.description || '',
        isStop: selectedItem.isStop || false,
        taxType: selectedItem.taxType || 1
      });
    } else if (visible && !selectedItem) {
      form.resetFields();
      form.setFieldsValue({
        isStop: false,
        taxType: 1
      });
    }
  }, [visible, selectedItem, form]);

  const handleClose = () => {
    form.resetFields();
    onClose();
  };

  const handleFormSubmit = async (values: any) => {
    await onSubmit(values);
  };

  return (
    <>
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <InboxOutlined />
            <span>{selectedItem ? '編輯商品' : '新增商品'}</span>
            {selectedItem && (
              <Tag color="blue" style={{ marginLeft: '8px' }}>
                編輯模式
              </Tag>
            )}
            {!selectedItem && (
              <Tag color="green" style={{ marginLeft: '8px' }}>
                新增模式
              </Tag>
            )}
          </div>
        }
        open={visible}
        onCancel={handleClose}
        onOk={() => form.submit()}
        width={1000}
        confirmLoading={loading}
        style={{ top: 20 }}
        maskClosable={true}
        keyboard={true}
        focusTriggerAfterClose={false}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
          initialValues={{ isStop: false, taxType: 1 }}
        >
          {/* 基本資訊區塊 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <InboxOutlined style={{ color: '#1890ff' }} />
                <span>基本資訊</span>
              </div>
            }
            size="small"
            style={{
              marginBottom: '16px',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
              border: '1px solid #e8e8e8'
            }}
            styles={{
              header: {
                backgroundColor: '#fafafa',
                borderBottom: '1px solid #f0f0f0',
                padding: isMobile ? '12px 16px' : '16px 20px'
              },
              body: {
                padding: isMobile ? '12px 16px' : '16px 20px'
              }
            }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={12} lg={12}>
                <Form.Item
                  label="商品名稱"
                  name="name"
                  rules={[
                    { required: true, message: '請輸入商品名稱' },
                    { max: 100, message: '商品名稱不能超過100個字符' }
                  ]}
                >
                  <Input
                    placeholder="請輸入商品名稱"
                    size="large"
                    style={{
                      borderRadius: '6px',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={12} lg={12}>
                <Form.Item
                  label="商品編號"
                  name="customNO"
                  rules={[
                    { required: true, message: '請輸入商品編號' },
                    { max: 50, message: '商品編號不能超過50個字符' }
                  ]}
                >
                  <Input
                    placeholder="請輸入商品編號"
                    size="large"
                    style={{
                      borderRadius: '6px',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={12} lg={12}>
                <Form.Item
                  label="國際條碼"
                  name="internationalBarCode"
                  rules={[
                    { max: 50, message: '國際條碼不能超過50個字符' }
                  ]}
                >
                  <Input
                    placeholder="請輸入國際條碼"
                    size="large"
                    style={{
                      borderRadius: '6px',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={12} lg={12}>
                <Form.Item
                  label="單位"
                  name="unit"
                  rules={[
                    { required: true, message: '請輸入單位' },
                    { max: 20, message: '單位不能超過20個字符' }
                  ]}
                >
                  <Input
                    placeholder="請輸入單位"
                    size="large"
                    style={{
                      borderRadius: '6px',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 分類和狀態區塊 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <ApartmentOutlined style={{ color: '#1890ff' }} />
                <span>分類設定</span>
              </div>
            }
            size="small"
            style={{
              marginBottom: '16px',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
              border: '1px solid #e8e8e8'
            }}
            styles={{
              header: {
                backgroundColor: '#fafafa',
                borderBottom: '1px solid #f0f0f0',
                padding: isMobile ? '12px 16px' : '16px 20px'
              },
              body: {
                padding: isMobile ? '12px 16px' : '16px 20px'
              }
            }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={18} md={18} lg={18}>
                <Form.Item
                  label={
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span>商品分類</span>
                      <Button
                        type="link"
                        size="small"
                        icon={<SettingOutlined />}
                        onClick={() => setIsCategoryModalVisible(true)}
                        style={{ padding: '0 4px', height: 'auto', fontSize: '12px' }}
                        title="快速管理分類"
                      >
                        管理分類
                      </Button>
                    </div>
                  }
                  name="itemCategoryID"
                  tooltip="可選擇任何層級的分類，選擇父分類將包含所有子分類商品。點擊「管理分類」可快速新增或編輯分類"
                >
                  <TreeSelect
                    placeholder="請選擇商品分類"
                    allowClear
                    showSearch
                    treeData={categoryTreeData}
                    treeDefaultExpandAll={true}
                    treeNodeFilterProp="title"
                    size="large"
                    style={{
                      width: '100%',
                      borderRadius: '6px',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                    }}
                    maxTagCount="responsive"
                    treeNodeLabelProp="title"
                    suffixIcon={<ApartmentOutlined />}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={6} md={6} lg={6}>
                <Form.Item
                  label="商品狀態"
                  name="isStop"
                  valuePropName="checked"
                  tooltip="預設為啟用狀態"
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    height: '40px',
                    padding: '8px 12px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px',
                    backgroundColor: '#fafafa'
                  }}>
                    <Switch
                      checkedChildren="停用"
                      unCheckedChildren="啟用"
                      size="default"
                    />
                  </div>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={12} lg={12}>
                <Form.Item
                  label="商品稅別"
                  name="taxType"
                  rules={[{ required: true, message: '請選擇商品稅別' }]}
                  tooltip="根據中華民國稅法規定選擇適當的稅別"
                >
                  <Select
                    placeholder="請選擇商品稅別"
                    size="large"
                    style={{
                      borderRadius: '6px',
                      boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                    }}
                  >
                    {itemTaxTypes.map(taxType => (
                      <Select.Option key={taxType.code} value={taxType.code}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span>{taxType.name}</span>
                          <Tag color={
                            taxType.code === 1 ? 'blue' :
                              taxType.code === 2 ? 'green' : 'orange'
                          } style={{ margin: 0 }}>
                            {taxType.taxRate * 100}%
                          </Tag>
                        </div>
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 商品描述區塊 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <FileTextOutlined style={{ color: '#1890ff' }} />
                <span>商品描述</span>
              </div>
            }
            size="small"
            style={{
              marginBottom: '16px',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
              border: '1px solid #e8e8e8'
            }}
            styles={{
              header: {
                backgroundColor: '#fafafa',
                borderBottom: '1px solid #f0f0f0',
                padding: isMobile ? '12px 16px' : '16px 20px'
              },
              body: {
                padding: isMobile ? '12px 16px' : '16px 20px'
              }
            }}
          >
            <Form.Item
              label="商品描述"
              name="description"
              rules={[
                { max: 500, message: '描述不能超過500個字符' }
              ]}
            >
              <Input.TextArea
                rows={4}
                placeholder="請輸入商品描述"
                showCount
                maxLength={500}
                style={{
                  borderRadius: '6px',
                  boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                }}
              />
            </Form.Item>
          </Card>

          {/* 價格設定區塊 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <MoneyCollectOutlined style={{ color: '#1890ff' }} />
                <span>價格設定</span>
                <Tag color="blue" style={{ marginLeft: '8px' }}>
                  {priceTypes.length} 種價格類型
                </Tag>
              </div>
            }
            size="small"
            style={{
              marginBottom: '16px',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
              border: '1px solid #e8e8e8'
            }}
            styles={{
              header: {
                backgroundColor: '#fafafa',
                borderBottom: '1px solid #f0f0f0',
                padding: isMobile ? '12px 16px' : '16px 20px'
              },
              body: {
                padding: isMobile ? '12px 16px' : '16px 20px'
              }
            }}
            extra={
              <Button
                type="link"
                size="small"
                icon={<SettingOutlined />}
                onClick={onPriceTypeModalOpen}
                style={{
                  borderRadius: '6px',
                  boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                }}
              >
                管理價格類型
              </Button>
            }
          >
            {priceTypes.length > 0 ? (
              <Row gutter={[16, 16]}>
                {priceTypes.map(priceType => (
                  <Col xs={24} sm={12} md={8} lg={6} key={priceType.priceTypeID}>
                    <Form.Item
                      label={
                        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                          <span>{priceType.name}價格</span>
                          {priceType.description && (
                            <Tooltip title={priceType.description}>
                              <Tag color="blue" style={{ fontSize: '10px', padding: '0 4px' }}>
                                ?
                              </Tag>
                            </Tooltip>
                          )}
                        </div>
                      }
                    >
                      <InputNumber
                        min={0}
                        step={0.01}
                        placeholder={`請輸入${priceType.name}價格`}
                        value={itemPrices[priceType.priceTypeID] || null}
                        onChange={(value) => {
                          const numericValue = value !== null && value !== undefined ? Number(value) : 0;
                          onItemPricesChange({
                            ...itemPrices,
                            [priceType.priceTypeID]: numericValue
                          });
                        }}
                        style={{
                          width: '100%',
                          borderRadius: '6px',
                          boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                        }}
                        size="large"
                        formatter={(value) => {
                          if (value === null || value === undefined) return '';
                          const numStr = String(value);
                          const parts = numStr.split('.');
                          parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                          return `$ ${parts.join('.')}`;
                        }}
                        parser={(value) => {
                          if (!value) return 0;
                          const cleaned = value.replace(/\$\s?/g, '').replace(/,/g, '');
                          const parsed = parseFloat(cleaned);
                          return isNaN(parsed) ? 0 : parsed;
                        }}
                        controls={{
                          upIcon: '▲',
                          downIcon: '▼'
                        }}
                        stringMode={false}
                      />
                    </Form.Item>
                  </Col>
                ))}
              </Row>
            ) : (
              <div style={{
                textAlign: 'center',
                color: '#999',
                padding: '40px 20px',
                background: '#f8f9fa',
                borderRadius: '8px',
                border: '2px dashed #d9d9d9'
              }}>
                <MoneyCollectOutlined style={{ fontSize: '48px', color: '#d9d9d9', marginBottom: '16px' }} />
                <p style={{ marginBottom: '16px', fontSize: '16px', fontWeight: '500' }}>尚未設定價格類型</p>
                <p style={{ marginBottom: '20px', color: '#666' }}>請先建立價格類型才能設定商品價格</p>
                <Button
                  type="primary"
                  size="large"
                  icon={<PlusOutlined />}
                  onClick={onPriceTypeModalOpen}
                  style={{
                    borderRadius: '6px',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                    fontWeight: '500'
                  }}
                >
                  新增價格類型
                </Button>
              </div>
            )}
          </Card>
        </Form>
      </Modal>

      {/* 分類管理組件 */}
      <ItemCategoryAdapter
        visible={isCategoryModalVisible}
        onClose={() => setIsCategoryModalVisible(false)}
        categories={categories}
        categoryTreeData={categoryTreeData}
        sortedCategoriesForDisplay={sortedCategoriesForDisplay}
        onDataChange={() => {
          onCategoryDataChange();
          message.success('分類資料已更新，請重新選擇分類');
        }}
      />
    </>
  );
};

export default ItemFormModal;
