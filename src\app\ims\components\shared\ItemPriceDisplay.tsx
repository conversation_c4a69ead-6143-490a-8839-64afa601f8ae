"use client";

import React from 'react';
import { Tag, Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { CashIcon } from './icons/CashIcon';

/**
 * 商品價格顯示組件
 * 
 * 遵循 FastERP 設計系統的極簡風格，與 Category 管理組件保持一致的視覺層次。
 * 提供清晰的價格資訊展示，支援響應式設計和多種顯示模式。
 * 
 * 特性：
 * - 極簡設計風格，與 Category 管理組件一致
 * - 支援多種價格類型顯示
 * - 響應式設計，適配移動端和桌面端
 * - 智能價格格式化和本地化
 * - 支援緊湊和完整顯示模式
 * 
 * @example
 * ```tsx
 * <ItemPriceDisplay
 *   prices={item.prices}
 *   priceTypes={priceTypes}
 *   mode="compact"
 *   maxDisplay={3}
 * />
 * ```
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

export interface ItemPrice {
  priceTypeID: string;
  price: number;
}

export interface PriceType {
  priceTypeID: string;
  name: string;
  description?: string;
}

export interface ItemPriceDisplayProps {
  /** 價格列表 */
  prices?: ItemPrice[];
  /** 價格類型定義 */
  priceTypes: PriceType[];
  /** 顯示模式 */
  mode?: 'compact' | 'full' | 'minimal';
  /** 最大顯示數量 */
  maxDisplay?: number;
  /** 是否顯示圖標 */
  showIcon?: boolean;
  /** 自定義樣式 */
  style?: React.CSSProperties;
  /** 自定義類名 */
  className?: string;
  /** 是否顯示詳細信息提示 */
  showTooltip?: boolean;
}

/**
 * 格式化價格顯示
 */
const formatPrice = (price: number): string => {
  return price.toLocaleString('zh-TW', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    style: 'currency',
    currency: 'TWD'
  });
};

/**
 * 獲取價格類型名稱
 */
const getPriceTypeName = (priceTypeID: string, priceTypes: PriceType[]): string => {
  const priceType = priceTypes.find(pt => pt.priceTypeID === priceTypeID);
  return priceType ? priceType.name : '未知類型';
};

/**
 * 獲取價格類型描述
 */
const getPriceTypeDescription = (priceTypeID: string, priceTypes: PriceType[]): string | undefined => {
  const priceType = priceTypes.find(pt => pt.priceTypeID === priceTypeID);
  return priceType?.description;
};

const ItemPriceDisplay: React.FC<ItemPriceDisplayProps> = ({
  prices = [],
  priceTypes = [],
  mode = 'compact',
  maxDisplay = 3,
  showIcon = true,
  style,
  className,
  showTooltip = true
}) => {
  // 資料驗證：確保 prices 是陣列且包含有效價格
  if (!Array.isArray(prices) || prices.length === 0) {
    return (
      <Tag 
        color="default" 
        icon={showIcon ? <CashIcon /> : undefined}
        style={{ 
          fontSize: '12px', 
          padding: '4px 8px',
          borderRadius: '4px',
          ...style 
        }}
        className={className}
      >
        未設定價格
      </Tag>
    );
  }

  // 篩選有效價格（價格大於0的項目）
  const validPrices = prices.filter(price =>
    price &&
    typeof price === 'object' &&
    typeof price.price === 'number' &&
    price.price > 0
  );

  if (validPrices.length === 0) {
    return (
      <Tag 
        color="default" 
        icon={showIcon ? <CashIcon /> : undefined}
        style={{ 
          fontSize: '12px', 
          padding: '4px 8px',
          borderRadius: '4px',
          ...style 
        }}
        className={className}
      >
        無有效價格
      </Tag>
    );
  }

  // 極簡模式：只顯示第一個價格
  if (mode === 'minimal') {
    const firstPrice = validPrices[0];
    const priceTypeName = getPriceTypeName(firstPrice.priceTypeID, priceTypes);
    const description = getPriceTypeDescription(firstPrice.priceTypeID, priceTypes);
    
    const content = (
      <Tag
        color="blue"
        style={{ 
          fontSize: '12px', 
          padding: '4px 8px',
          borderRadius: '4px',
          fontWeight: 500,
          ...style 
        }}
        className={className}
      >
        {showIcon && <CashIcon style={{ marginRight: 4 }} />}
        {formatPrice(firstPrice.price)}
      </Tag>
    );

    if (showTooltip && description) {
      return (
        <Tooltip title={`${priceTypeName}: ${description}`}>
          {content}
        </Tooltip>
      );
    }

    return content;
  }

  // 完整模式：顯示所有價格
  if (mode === 'full') {
    return (
      <div 
        style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          gap: '4px',
          ...style 
        }}
        className={className}
      >
        {validPrices.map((price, index) => {
          const priceTypeName = getPriceTypeName(price.priceTypeID, priceTypes);
          const description = getPriceTypeDescription(price.priceTypeID, priceTypes);
          
          const tag = (
            <Tag
              key={`${price.priceTypeID}-${index}`}
              color="blue"
              style={{ 
                margin: '0',
                fontSize: '11px', 
                padding: '2px 6px',
                borderRadius: '4px',
                fontWeight: 500
              }}
            >
              {priceTypeName}: {formatPrice(price.price)}
            </Tag>
          );

          if (showTooltip && description) {
            return (
              <Tooltip key={`${price.priceTypeID}-${index}`} title={description}>
                {tag}
              </Tooltip>
            );
          }

          return tag;
        })}
      </div>
    );
  }

  // 緊湊模式（默認）：顯示限定數量的價格
  const displayPrices = validPrices.slice(0, maxDisplay);
  const remainingCount = validPrices.length - maxDisplay;

  return (
    <div 
      style={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: '2px',
        ...style 
      }}
      className={className}
    >
      {displayPrices.map((price, index) => {
        const priceTypeName = getPriceTypeName(price.priceTypeID, priceTypes);
        const description = getPriceTypeDescription(price.priceTypeID, priceTypes);
        
        const tag = (
          <Tag
            key={`${price.priceTypeID}-${index}`}
            color="blue"
            style={{ 
              margin: '1px 0',
              fontSize: '11px', 
              padding: '2px 6px',
              borderRadius: '4px',
              fontWeight: 500
            }}
          >
            {priceTypeName}: {formatPrice(price.price)}
          </Tag>
        );

        if (showTooltip && description) {
          return (
            <Tooltip key={`${price.priceTypeID}-${index}`} title={description}>
              {tag}
            </Tooltip>
          );
        }

        return tag;
      })}
      
      {remainingCount > 0 && (
        <Tooltip title={`還有 ${remainingCount} 個價格類型`}>
          <Tag 
            color="geekblue" 
            style={{ 
              margin: '1px 0',
              fontSize: '11px', 
              padding: '2px 6px',
              borderRadius: '4px',
              fontWeight: 500,
              cursor: 'pointer'
            }}
            icon={<InfoCircleOutlined />}
          >
            +{remainingCount} 更多
          </Tag>
        </Tooltip>
      )}
    </div>
  );
};

export default ItemPriceDisplay;
