"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { Card, Spin, Table, Form, InputNumber, Button, Space, Modal, message, Switch, Select, Tag, Tooltip, Popconfirm, Typography, Statistic, Row, Col, Progress, TreeSelect, List, Descriptions } from "antd";
import { EditOutlined, DeleteOutlined, CheckCircleOutlined, StopOutlined, SettingOutlined,FolderOutlined,AppstoreOutlined,MoneyCollectOutlined,UnorderedListOutlined,TagsOutlined,ShopOutlined,SyncOutlined,FileAddOutlined,ExperimentOutlined } from "@ant-design/icons";

// Services and Types
import { getItemList, getItem, deleteItem, editItem, addItem, generateTestItems, getItemTaxTypes, type Item, type ItemPrice, type PriceType, type ItemTaxTypeOption } from '@/services/ims/ItemService';
import { getItemCategoryList, buildCategoryTree, getCategoryHierarchyName, type ItemCategory } from '@/services/ims/ItemCategoryService';
import { getPriceTypeList } from '@/services/ims/PriceTypeService';
import { getItemPriceList, addItemPrice, editItemPrice } from '@/services/ims/ItemPriceService';

// Components
import PriceTypeManagement from '@/app/ims/components/PriceTypeManagement';
import ItemCategoryAdapter from '@/app/ims/components/shared/ItemCategoryAdapter';
import ItemFormModal from '@/app/ims/components/ItemFormModal';
import TestDataCountInput from '@/app/ims/components/TestDataCountInput';
import FilterSearchContainer from '@/app/ims/components/shared/FilterSearchContainer';
import ResponsiveStyles from '@/app/ims/components/shared/ResponsiveStyles';
import { FilterOption } from '@/app/ims/types/filter';

// Data Validation Utils
import { processApiResponse, safeString, safeBoolean } from '@/utils/dataValidation';

const { Title } = Typography;

// 整合資料介面
interface IntegratedData {
  items: Item[];
  categories: ItemCategory[];
  priceTypes: PriceType[];
  itemPrices: ItemPrice[];
}

// 統計資料介面
interface StatsData {
  totalItems: number;
  activeItems: number;
  totalCategories: number;
  totalPriceTypes: number;
}

// 強化的API回應驗證函數
const validateItemApiResponse = (response: any): { success: boolean; data: Item[]; message?: string } => {
  console.log('🔍 驗證商品API回應:', response);

  // 檢查基本回應結構
  if (!response || typeof response !== 'object') {
    console.error('❌ API回應格式錯誤: 不是物件', response);
    return { success: false, data: [], message: 'API回應格式錯誤' };
  }

  // 檢查success欄位
  if (typeof response.success !== 'boolean') {
    console.error('❌ API回應缺少success欄位或格式錯誤', response);
    return { success: false, data: [], message: 'API回應格式錯誤：缺少success欄位' };
  }

  // 如果API回應失敗
  if (!response.success) {
    console.warn('⚠️ API回應失敗:', response.message);
    return {
      success: false,
      data: [],
      message: response.message || 'API操作失敗'
    };
  }

  // 驗證data欄位
  if (!response.data) {
    console.warn('⚠️ API回應成功但無資料');
    return { success: true, data: [], message: '無資料' };
  }

  // 確保data是陣列
  if (!Array.isArray(response.data)) {
    console.error('❌ API回應的data不是陣列:', typeof response.data, response.data);
    return { success: false, data: [], message: '資料格式錯誤：期望陣列格式' };
  }

  // 驗證陣列中的每個項目並進行資料類型轉換
  const validItems = response.data.filter((item: any) => {
    if (!item || typeof item !== 'object') {
      console.warn('⚠️ 無效的商品項目:', item);
      return false;
    }

    // 檢查必要欄位 - 使用camelCase格式
    const requiredFields = ['itemID', 'name', 'customNO', 'unit'];
    const hasRequired = requiredFields.every(field => {
      const hasField = item.hasOwnProperty(field) && item[field] !== null && item[field] !== undefined;
      if (!hasField) {
        console.warn(`⚠️ 商品缺少必要欄位 ${field}:`, item);
      }
      return hasField;
    });

    if (hasRequired) {
      // 確保 itemCategoryID 是字串格式
      if (item.itemCategoryID !== null && item.itemCategoryID !== undefined) {
        item.itemCategoryID = String(item.itemCategoryID);
      }

      // 確保其他 ID 欄位也是字串格式
      item.itemID = String(item.itemID);

      // 調試日誌 (僅開發環境)
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 商品資料轉換:', {
          itemID: item.itemID,
          name: item.name,
          itemCategoryID: item.itemCategoryID,
          categoryType: typeof item.itemCategoryID
        });
      }
    }

    return hasRequired;
  });

  console.log(`✅ 商品資料驗證完成: ${validItems.length}/${response.data.length} 個有效項目`);

  return {
    success: true,
    data: validItems,
    message: `成功載入 ${validItems.length} 個商品`
  };
};

const ItemPage = () => {
  // 主要狀態
  const [data, setData] = useState<IntegratedData>({
    items: [],
    categories: [],
    priceTypes: [],
    itemPrices: []
  });
  const [stats, setStats] = useState<StatsData>({
    totalItems: 0,
    activeItems: 0,
    totalCategories: 0,
    totalPriceTypes: 0
  });
  const [loading, setLoading] = useState(true);

  // 表單和互動視窗狀態
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [form] = Form.useForm();

  // 篩選後的商品列表狀態
  const [filteredItems, setFilteredItems] = useState<Item[]>([]);

  // 分頁相關狀態
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 測試資料產生狀態
  const [isGeneratingTestData, setIsGeneratingTestData] = useState(false);
  const [testDataCount, setTestDataCount] = useState<number | null>(20000);
  const [isTestDataModalVisible, setIsTestDataModalVisible] = useState(false);

  // 商品稅別狀態
  const [itemTaxTypes, setItemTaxTypes] = useState<ItemTaxTypeOption[]>([]);

  // CRUD 互動視窗狀態
  const [isCategoryModalVisible, setIsCategoryModalVisible] = useState(false);
  const [isPriceTypeModalVisible, setIsPriceTypeModalVisible] = useState(false);

  // 商品價格管理狀態
  const [itemPrices, setItemPrices] = useState<Record<string, number>>({});

  // 移動端檢測狀態
  const [isMobile, setIsMobile] = useState(false);

  // 檢查分類是否匹配（支援父分類選擇）
  const isCategoryMatch = useCallback((itemCategoryId: string | null, selectedCategoryId: string, categories: ItemCategory[]): boolean => {
    if (!itemCategoryId || !selectedCategoryId) return false;

    // 直接匹配
    if (itemCategoryId === selectedCategoryId) return true;

    // 檢查是否為子分類
    const findInTree = (cats: ItemCategory[], targetId: string, parentId: string): boolean => {
      for (const cat of cats) {
        if (cat.itemCategoryID === parentId) {
          // 找到父分類，檢查其所有子分類
          const checkChildren = (children: ItemCategory[]): boolean => {
            for (const child of children) {
              if (child.itemCategoryID === targetId) return true;
              if (child.children && child.children.length > 0) {
                if (checkChildren(child.children)) return true;
              }
            }
            return false;
          };

          if (cat.children && cat.children.length > 0) {
            return checkChildren(cat.children);
          }
        }

        if (cat.children && cat.children.length > 0) {
          if (findInTree(cat.children, targetId, parentId)) return true;
        }
      }
      return false;
    };

    const tree = buildCategoryTree(categories);
    return findInTree(tree, itemCategoryId, selectedCategoryId);
  }, []);

  // 載入所有資料的主要函數
  const loadAllData = useCallback(async () => {
    setLoading(true);
    console.log('🔄 開始載入IMS商品模組資料...');

    try {
      // 並行載入所有必要資料
      const [itemsRes, categoriesRes, priceTypesRes, itemPricesRes, itemTaxTypesRes] = await Promise.all([
        getItemList(),
        getItemCategoryList(),
        getPriceTypeList(),
        getItemPriceList(),
        getItemTaxTypes()
      ]);

      console.log('📊 API回應狀態:', {
        items: { success: itemsRes.success, message: itemsRes.message },
        categories: { success: categoriesRes.success, message: categoriesRes.message },
        priceTypes: { success: priceTypesRes.success, message: priceTypesRes.message },
        itemPrices: { success: itemPricesRes.success, message: itemPricesRes.message },
        itemTaxTypes: { success: itemTaxTypesRes.success, message: itemTaxTypesRes.message }
      });

      // 使用強化的驗證函數處理商品資料
      const itemsResult = validateItemApiResponse(itemsRes);

      // 調試：檢查商品的分類ID (僅開發環境)
      if (process.env.NODE_ENV === 'development' && itemsResult.success && itemsResult.data.length > 0) {
        const itemsWithCategories = itemsResult.data.filter(item => item.itemCategoryID);
        console.log('🔍 有分類的商品數量:', itemsWithCategories.length, '/ 總商品數:', itemsResult.data.length);
        if (itemsWithCategories.length > 0) {
          console.log('🔍 商品分類ID範例:', itemsWithCategories.slice(0, 3).map(item => ({
            name: item.name,
            itemCategoryID: item.itemCategoryID,
            categoryType: typeof item.itemCategoryID
          })));
        }
      }

      // 使用通用驗證函數處理其他資料
      const categoriesResult = processApiResponse<ItemCategory>(categoriesRes, '分類');

      // 調試：檢查分類資料 (僅開發環境)
      if (process.env.NODE_ENV === 'development' && categoriesResult.success && categoriesResult.data.length > 0) {
        console.log('🔍 可用分類數量:', categoriesResult.data.length);
        console.log('🔍 分類資料範例:', categoriesResult.data.slice(0, 3).map(cat => ({
          id: cat.itemCategoryID,
          name: cat.name,
          idType: typeof cat.itemCategoryID
        })));
      }
      const priceTypesResult = processApiResponse<PriceType>(priceTypesRes, '價格類型');
      const itemPricesResult = processApiResponse<ItemPrice>(itemPricesRes, '商品價格');
      const itemTaxTypesResult = processApiResponse<ItemTaxTypeOption>(itemTaxTypesRes, '商品稅別');

      // 更新狀態
      const newData: IntegratedData = {
        items: itemsResult.data,
        categories: categoriesResult.data,
        priceTypes: priceTypesResult.data,
        itemPrices: itemPricesResult.data
      };

      setData(newData);
      setItemTaxTypes(itemTaxTypesResult.data);

      // 計算統計資料
      const newStats: StatsData = {
        totalItems: newData.items.length,
        activeItems: newData.items.filter(item => !safeBoolean(item.isStop)).length,
        totalCategories: newData.categories.length,
        totalPriceTypes: newData.priceTypes.length
      };

      setStats(newStats);

      // 檢查載入結果
      const errors = [
        !itemsResult.success && `商品: ${itemsResult.message}`,
        !categoriesResult.success && `分類: ${categoriesResult.message}`,
        !priceTypesResult.success && `價格類型: ${priceTypesResult.message}`,
        !itemPricesResult.success && `商品價格: ${itemPricesResult.message}`,
        !itemTaxTypesResult.success && `商品稅別: ${itemTaxTypesResult.message}`
      ].filter(Boolean);

      if (errors.length > 0) {
        console.warn('⚠️ 部分資料載入失敗:', errors);
        message.warning(`部分資料載入失敗: ${errors.join(', ')}`);
      } else {
        console.log('✅ 所有資料載入成功');
        message.success('資料載入成功');
      }

    } catch (error) {
      console.error('❌ 載入資料時發生錯誤:', error);
      message.error('載入資料失敗，請檢查網路連接和後端服務');
    } finally {
      setLoading(false);
    }
  }, []);

  // 重新載入特定類型的資料
  const reloadData = useCallback(async (type: keyof IntegratedData) => {
    try {
      console.log(`🔄 重新載入${type}...`);

      const apiMap = {
        items: getItemList,
        categories: getItemCategoryList,
        priceTypes: getPriceTypeList,
        itemPrices: getItemPriceList
      };

      const apiFunction = apiMap[type];
      if (!apiFunction) {
        console.warn(`⚠️ 未知的資料類型: ${type}`);
        return;
      }

      const response = await apiFunction();

      let result;
      if (type === 'items') {
        result = validateItemApiResponse(response);
      } else {
        result = processApiResponse(response, type);
      }

      if (result.success) {
        setData(prev => ({
          ...prev,
          [type]: result.data
        }));

        // 更新統計資料
        if (type === 'items') {
          setStats(prev => ({
            ...prev,
            totalItems: result.data.length,
            activeItems: (result.data as Item[]).filter(item => !safeBoolean(item.isStop)).length
          }));
        } else if (type === 'categories') {
          setStats(prev => ({
            ...prev,
            totalCategories: result.data.length
          }));
        } else if (type === 'priceTypes') {
          setStats(prev => ({
            ...prev,
            totalPriceTypes: result.data.length
          }));
        }

        console.log(`✅ ${type} 重新載入成功: ${result.data.length} 項`);
      } else {
        console.warn(`⚠️ ${type} 重新載入失敗:`, result.message);
        message.error(`重新載入${type}失敗: ${result.message}`);
      }
    } catch (error) {
      console.error(`❌ 重新載入${type}時發生錯誤:`, error);
      message.error(`重新載入${type}失敗`);
    }
  }, []);

  // 初始載入
  useEffect(() => {
    loadAllData();
  }, [loadAllData]);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  /**
   * 檢查篩選值是否有效（非空）
   * @param value 要檢查的值
   * @returns 是否為有效的篩選值
   */
  const isValidFilterValue = useCallback((value: any): boolean => {
    if (value === undefined || value === null || value === '') {
      return false;
    }

    // 對於陣列類型（如多選分類），檢查是否為非空陣列
    if (Array.isArray(value)) {
      return value.length > 0;
    }

    // 對於字串類型，檢查是否為非空字串
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }

    return true;
  }, []);

  // 初始化篩選後的商品列表
  useEffect(() => {
    setFilteredItems(data.items);
  }, [data.items]);

  // 分類名稱快取 - 使用 useMemo 避免重複計算
  const categoryNameCache = useMemo(() => {
    const cache = new Map<string, string>();

    // 預先計算所有分類的層級名稱
    data.categories.forEach(category => {
      if (category.itemCategoryID) {
        const hierarchyName = getCategoryHierarchyName(data.categories, category.itemCategoryID);
        cache.set(category.itemCategoryID, hierarchyName);
      }
    });

    // 調試日誌 (僅在開發環境)
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 分類名稱快取已建立，包含', cache.size, '個分類');
      console.log('📋 快取內容:', Array.from(cache.entries()));
    }

    return cache;
  }, [data.categories]);

  // 獲取分類名稱的輔助函數 - 使用快取提升效能
  const getCategoryName = useCallback((categoryId: string | null): string => {
    if (!categoryId) return '未分類';

    // 確保 categoryId 是字串格式
    const categoryIdStr = String(categoryId);

    // 從快取中獲取結果
    const cachedName = categoryNameCache.get(categoryIdStr);
    if (cachedName) {
      return cachedName;
    }

    // 快取中沒有找到，返回預設值
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ getCategoryName - category not found in cache for ID:', categoryIdStr);
    }
    return '未知分類';
  }, [categoryNameCache]);

  // 移動端列表渲染
  const renderMobileList = () => {
    const currentPageData = filteredItems.slice(
      (currentPage - 1) * pageSize,
      currentPage * pageSize
    );

    return (
      <div className="mobile-list">
        {currentPageData.map((item) => (
          <Card
            key={item.itemID}
            size="small"
            className="mobile-item"
            style={{ marginBottom: 12 }}
            actions={[
              <Button
                key="edit"
                type="link"
                icon={<EditOutlined />}
                onClick={() => showEditModal(item)}
                size="small"
              >
                編輯
              </Button>,
              <Popconfirm
                key="delete"
                title="確定要刪除此商品嗎？"
                onConfirm={() => handleDelete(item.itemID)}
                okText="確定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  danger
                  icon={<DeleteOutlined />}
                  size="small"
                >
                  刪除
                </Button>
              </Popconfirm>
            ]}
          >
            <div className="mobile-header">
              <div className="mobile-title">
                <Typography.Text strong style={{ fontSize: '16px' }}>
                  {safeString(item.name)}
                </Typography.Text>
                <div style={{ marginTop: 4 }}>
                  <Tag color={item.isStop ? 'red' : 'green'}>
                    {item.isStop ? '停用' : '啟用'}
                  </Tag>
                </div>
              </div>
            </div>

            <div className="mobile-content" style={{ marginTop: 12 }}>
              <Descriptions size="small" column={1} colon={false}>
                <Descriptions.Item label="商品編號">
                  <Typography.Text code>{safeString(item.customNO)}</Typography.Text>
                </Descriptions.Item>
                <Descriptions.Item label="國際條碼">
                  {safeString(item.internationalBarCode) || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="單位">
                  <Tag color="blue">{safeString(item.unit)}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="分類">
                  <Tag color="purple">{getCategoryName(item.itemCategoryID)}</Tag>
                </Descriptions.Item>
                {item.description && (
                  <Descriptions.Item label="描述">
                    <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                      {safeString(item.description)}
                    </Typography.Text>
                  </Descriptions.Item>
                )}
              </Descriptions>
            </div>
          </Card>
        ))}

        {/* 移動端分頁 */}
        {filteredItems.length > pageSize && (
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Button>
              <Button
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
                icon={<span>‹</span>}
              >
                上一頁
              </Button>
              <Button disabled>
                {currentPage} / {Math.ceil(filteredItems.length / pageSize)}
              </Button>
              <Button
                disabled={currentPage >= Math.ceil(filteredItems.length / pageSize)}
                onClick={() => setCurrentPage(currentPage + 1)}
                icon={<span>›</span>}
              >
                下一頁
              </Button>
            </Button>
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              共 {filteredItems.length} 項，每頁 {pageSize} 項
            </div>
          </div>
        )}
      </div>
    );
  };

  // 處理刪除
  const handleDelete = async (itemId: string) => {
    if (!itemId) {
      message.error('商品ID不能為空');
      return;
    }

    Modal.confirm({
      title: '確認刪除',
      content: '確定要刪除此庫存品嗎？此操作無法復原。',
      okText: '確定刪除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          console.log('🔄 刪除商品:', itemId);
          const response = await deleteItem(itemId);

          if (response.success) {
            message.success('商品刪除成功');
            reloadData('items');
          } else {
            message.error(response.message || '刪除失敗');
          }
        } catch (error) {
          console.error('❌ 刪除商品時發生錯誤:', error);
          message.error('刪除失敗，請重試');
        }
      }
    });
  };

  // 顯示編輯互動視窗
  const showEditModal = async (item: Item) => {
    if (!item || !item.itemID) {
      message.error('商品資料無效');
      return;
    }

    setLoading(true);
    try {
      console.log('🔄 載入商品詳細資料:', item.itemID);

      // 獲取最新的商品資料
      const itemResponse = await getItem(item.itemID);
      if (itemResponse.success && itemResponse.data) {
        const latestItem = itemResponse.data;
        setSelectedItem(latestItem);

        // 設定表單值
        form.setFieldsValue({
          name: safeString(latestItem.name, ''),
          customNO: safeString(latestItem.customNO, ''),
          internationalBarCode: safeString(latestItem.internationalBarCode, ''),
          unit: safeString(latestItem.unit, ''),
          itemCategoryID: latestItem.itemCategoryID || null,
          description: safeString(latestItem.description, ''),
          isStop: safeBoolean(latestItem.isStop, false),
          taxType: latestItem.taxType || 1 // ItemTaxType.Taxable
        });

        // 載入商品價格 - 強化資料驗證
        if (Array.isArray(latestItem.prices) && latestItem.prices.length > 0) {
          const priceMap: Record<string, number> = {};
          latestItem.prices.forEach(price => {
            // 確保價格資料有效
            if (price &&
              typeof price === 'object' &&
              price.priceTypeID &&
              typeof price.price === 'number' &&
              price.price >= 0) {
              priceMap[price.priceTypeID] = price.price;
            }
          });
          setItemPrices(priceMap);
          console.log('✅ 載入商品價格:', priceMap);
        } else {
          setItemPrices({});
          console.log('ℹ️ 商品無價格資料');
        }

        setIsModalVisible(true);
        console.log('✅ 商品資料載入成功');
      } else {
        message.error('無法載入商品詳細資料: ' + (itemResponse.message || '未知錯誤'));
      }
    } catch (error) {
      console.error('❌ 載入商品詳細資料時發生錯誤:', error);
      message.error('載入商品詳細資料失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 顯示新增互動視窗
  const showAddModal = () => {
    setSelectedItem(null);
    form.resetFields();
    form.setFieldsValue({
      isStop: false,  // 預設為啟用狀態 (false = 啟用, true = 停用)
      taxType: 1  // 預設為應稅 (ItemTaxType.Taxable)
    });
    setItemPrices({}); // 重置價格
    setIsModalVisible(true);
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      console.log('🔄 提交商品資料:', values);

      // 資料驗證
      if (!values.name || values.name.trim() === '') {
        message.error('商品名稱不能為空');
        return;
      }
      if (!values.customNO || values.customNO.trim() === '') {
        message.error('商品編號不能為空');
        return;
      }
      if (!values.unit || values.unit.trim() === '') {
        message.error('單位不能為空');
        return;
      }

      const itemData = {
        ...values,
        itemID: selectedItem?.itemID,
        isStop: safeBoolean(values.isStop, false)
      };

      const response = selectedItem
        ? await editItem(itemData)
        : await addItem(itemData);

      if (response.success) {
        // 如果商品操作成功，處理價格資料
        if (Object.keys(itemPrices).length > 0) {
          try {
            const itemId = selectedItem?.itemID || response.data?.itemID;
            if (itemId) {
              // 為每個有價格的價格類型創建或更新價格記錄
              const pricePromises = Object.entries(itemPrices).map(async ([priceTypeID, price]) => {
                // 確保價格是有效數值且大於0
                const numericPrice = Number(price);
                if (!isNaN(numericPrice) && numericPrice > 0) {
                  const priceData = {
                    itemID: itemId,
                    priceTypeID,
                    price: numericPrice
                  };

                  // 檢查是否已存在價格記錄
                  const existingPrice = selectedItem?.prices?.find(p => p.priceTypeID === priceTypeID);

                  return existingPrice
                    ? await editItemPrice({ ...existingPrice, price: numericPrice })
                    : await addItemPrice(priceData);
                }
                return Promise.resolve({ success: true });
              });

              await Promise.all(pricePromises);
              console.log('✅ 商品價格操作成功');
            }
          } catch (priceError) {
            console.warn('⚠️ 價格操作部分失敗:', priceError);
            message.warning('商品保存成功，但部分價格設定失敗');
          }
        }

        message.success(selectedItem ? '商品更新成功' : '商品新增成功');
        setIsModalVisible(false);
        form.resetFields();
        setSelectedItem(null);
        setItemPrices({});
        reloadData('items');
        reloadData('itemPrices');
        console.log('✅ 商品操作成功');
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error('❌ 提交商品時發生錯誤:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 建構分類樹狀結構用於選擇器
  const categoryTreeData = useMemo(() => {
    const tree = buildCategoryTree(data.categories);

    const convertToTreeSelectData = (categories: ItemCategory[]): any[] => {
      return categories.map(category => ({
        title: category.name,
        value: category.itemCategoryID,
        key: category.itemCategoryID,
        children: category.children && category.children.length > 0
          ? convertToTreeSelectData(category.children)
          : undefined,
        // 允許選擇任何節點（父節點或子節點）
        disabled: false,
        selectable: true
      }));
    };

    return convertToTreeSelectData(tree);
  }, [data.categories]);



  // 建構按階層排序的分類列表用於顯示
  const sortedCategoriesForDisplay = useMemo(() => {
    const tree = buildCategoryTree(data.categories);
    const flattenedCategories: ItemCategory[] = [];

    // 遞迴展開樹狀結構，保持階層順序
    const flattenTree = (categories: ItemCategory[], level: number = 0) => {
      categories.forEach(category => {
        // 添加層級資訊用於顯示縮排
        const categoryWithLevel = { ...category, level };
        flattenedCategories.push(categoryWithLevel);

        // 如果有子分類，遞迴處理
        if (category.children && category.children.length > 0) {
          flattenTree(category.children, level + 1);
        }
      });
    };

    flattenTree(tree);
    return flattenedCategories;
  }, [data.categories]);



  // 商品篩選邏輯
  const applyItemFilters = useCallback((
    items: Item[],
    searchText: string,
    activeFilters: string[],
    filterValues: Record<string, any>
  ): Item[] => {
    if (!Array.isArray(items)) {
      console.warn('⚠️ items 不是陣列:', items);
      return [];
    }

    return items.filter(item => {
      if (!item) return false;

      // 搜尋文字篩選
      const matchesSearch = !searchText ||
        safeString(item.name).toLowerCase().includes(searchText.toLowerCase()) ||
        safeString(item.customNO).toLowerCase().includes(searchText.toLowerCase()) ||
        safeString(item.internationalBarCode).toLowerCase().includes(searchText.toLowerCase());

      // 動態篩選條件
      const matchesFilters = activeFilters.every(filterKey => {
        const value = filterValues[filterKey];
        if (!value) return true;

        try {
          switch (filterKey) {
            case "category":
              // 支援多選分類篩選
              if (Array.isArray(value)) {
                return value.some(categoryId => {
                  if (!categoryId) return false;
                  return isCategoryMatch(item.itemCategoryID, categoryId, data.categories);
                });
              } else {
                return isCategoryMatch(item.itemCategoryID, value, data.categories);
              }

            case "status":
              return Array.isArray(value)
                ? value.some(v => (v === 'active' && !safeBoolean(item.isStop)) || (v === 'inactive' && safeBoolean(item.isStop)))
                : (value === 'active' && !safeBoolean(item.isStop)) || (value === 'inactive' && safeBoolean(item.isStop));

            case "customNO":
              return safeString(item.customNO).toLowerCase().includes(value.toLowerCase());

            case "name":
              return safeString(item.name).toLowerCase().includes(value.toLowerCase());

            case "internationalBarCode":
              return safeString(item.internationalBarCode).toLowerCase().includes(value.toLowerCase());

            case "unit":
              return safeString(item.unit).toLowerCase().includes(value.toLowerCase());

            default:
              console.warn(`⚠️ 未知的篩選類型: ${filterKey}`);
              return true;
          }
        } catch (error) {
          console.error(`❌ 篩選邏輯錯誤 (${filterKey}):`, error);
          return true;
        }
      });

      return matchesSearch && matchesFilters;
    });
  }, [data.categories, isCategoryMatch]);

  /**
   * 處理測試資料產生（使用自訂 Modal 支持動態更新）
   */
  const handleGenerateTestData = () => {
    setIsTestDataModalVisible(true);
  };

  /**
   * 執行測試資料產生
   */
  const executeTestDataGeneration = async () => {
    setIsTestDataModalVisible(false);
    setIsGeneratingTestData(true);

    try {
      // 使用當前的數量值，確保使用最新的狀態
      const count = testDataCount || 20000;

      // 驗證數量範圍
      if (count < 1 || count > 50000) {
        message.error('測試資料數量必須在 1 到 50,000 之間');
        return;
      }

      console.log('🚀 開始產生測試資料:', count, '筆');

      // 呼叫簡化的測試資料產生函數
      const result = await generateTestItems(count);

      if (result.success) {
        message.success(`${result.message} (實際產生 ${count.toLocaleString()} 筆)`);
        // 重新載入商品資料
        await loadAllData();
      } else {
        message.error(result.message);
      }

    } catch (error: any) {
      console.error('❌ 產生測試資料時發生錯誤:', error);
      message.error('啟動測試資料產生失敗，請稍後再試');
    } finally {
      setIsGeneratingTestData(false);
    }
  };

  // 定義篩選選項供 AdvancedFilterComponent 使用
  const itemFilterOptions: FilterOption[] = useMemo(() => [
    {
      label: "商品分類",
      value: "category",
      type: "treeSelect",
      treeData: categoryTreeData,
      width: 250
    },
    {
      label: "商品狀態",
      value: "status",
      children: [
        { label: "啟用", value: "active" },
        { label: "停用", value: "inactive" },
      ],
    },
    {
      label: "商品編號",
      value: "customNO",
      type: "input",
      placeholder: "輸入商品編號"
    },
    {
      label: "商品名稱",
      value: "name",
      type: "input",
      placeholder: "輸入商品名稱"
    },
    {
      label: "國際條碼",
      value: "internationalBarCode",
      type: "input",
      placeholder: "輸入國際條碼"
    },
    {
      label: "單位",
      value: "unit",
      type: "input",
      placeholder: "輸入單位"
    },
  ], [categoryTreeData]);

  // 表格列定義
  const columns = [
    {
      title: '商品編號',
      dataIndex: 'customNO',
      key: 'customNO',
      width: 120,
      sorter: (a: Item, b: Item) => safeString(a.customNO).localeCompare(safeString(b.customNO)),
      render: (text: string, record: Item) => {
        return safeString(text || record.customNO);
      }
    },
    {
      title: '商品名稱',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
      sorter: (a: Item, b: Item) => safeString(a.name).localeCompare(safeString(b.name)),
      render: (text: string, record: Item) => {
        return safeString(text || record.name);
      }
    },
    {
      title: '國際條碼',
      dataIndex: 'internationalBarCode',
      key: 'internationalBarCode',
      width: 150,
      sorter: (a: Item, b: Item) => safeString(a.internationalBarCode).localeCompare(safeString(b.internationalBarCode)),
      render: (text: string) => safeString(text)
    },
    {
      title: '單位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80,
      sorter: (a: Item, b: Item) => safeString(a.unit).localeCompare(safeString(b.unit)),
      render: (text: string) => safeString(text)
    },
    {
      title: '分類',
      dataIndex: 'itemCategoryID',
      key: 'itemCategoryID',
      width: 120,
      sorter: (a: Item, b: Item) => {
        const categoryNameA = getCategoryName(a.itemCategoryID);
        const categoryNameB = getCategoryName(b.itemCategoryID);
        return categoryNameA.localeCompare(categoryNameB);
      },
      render: (categoryId: string | null) => {
        const categoryName = getCategoryName(categoryId);
        return (
          <Tag color="purple" icon={<FolderOutlined />}>
            {categoryName}
          </Tag>
        );
      }
    },
    {
      title: '價格資訊',
      key: 'priceInfo',
      width: 200,
      render: (_: any, record: Item) => {
        // 強化資料驗證：確保 prices 是陣列且包含有效價格
        const prices = record.prices;

        if (!Array.isArray(prices) || prices.length === 0) {
          return (
            <Tag color="default" icon={<MoneyCollectOutlined />}>
              未設定價格
            </Tag>
          );
        }

        // 篩選有效價格（價格大於0的項目）
        const validPrices = prices.filter(price =>
          price &&
          typeof price === 'object' &&
          typeof price.price === 'number' &&
          price.price > 0
        );

        if (validPrices.length === 0) {
          return (
            <Tag color="default" icon={<MoneyCollectOutlined />}>
              無有效價格
            </Tag>
          );
        }

        // 顯示價格資訊
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2px' }}>
            {validPrices.slice(0, 3).map((price, index) => {
              // 尋找對應的價格類型名稱
              const priceType = data.priceTypes.find(pt => pt.priceTypeID === price.priceTypeID);
              const priceTypeName = priceType ? priceType.name : '未知類型';

              return (
                <Tag
                  key={`${price.priceTypeID}-${index}`}
                  color="blue"
                  style={{ margin: '1px 0', fontSize: '11px', padding: '2px 6px' }}
                >
                  {priceTypeName}: ${price.price.toLocaleString('zh-TW', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                  })}
                </Tag>
              );
            })}
            {validPrices.length > 3 && (
              <Tag color="geekblue" style={{ margin: '1px 0', fontSize: '11px', padding: '2px 6px' }}>
                +{validPrices.length - 3} 更多
              </Tag>
            )}
          </div>
        );
      }
    },
    {
      title: '商品稅別',
      dataIndex: 'taxType',
      key: 'taxType',
      width: 100,
      render: (taxType: number) => {
        const getTaxTypeName = (type: number) => {
          switch (type) {
            case 1: return '應稅';
            case 2: return '免稅';
            case 3: return '零稅率';
            default: return '應稅';
          }
        };
        const getTaxTypeColor = (type: number) => {
          switch (type) {
            case 1: return 'blue';
            case 2: return 'green';
            case 3: return 'orange';
            default: return 'blue';
          }
        };
        const typeName = getTaxTypeName(taxType || 1);
        const typeColor = getTaxTypeColor(taxType || 1);
        return (
          <Tag color={typeColor}>
            {typeName}
          </Tag>
        );
      }
    },
    {
      title: '狀態',
      dataIndex: 'isStop',
      key: 'isStop',
      width: 80,
      render: (isStop: boolean) => (
        <Tag
          color={safeBoolean(isStop) ? 'red' : 'green'}
          icon={safeBoolean(isStop) ? <StopOutlined /> : <CheckCircleOutlined />}
        >
          {safeBoolean(isStop) ? '停用' : '啟用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: Item) => (
        <Space size="small">
          <Tooltip title="編輯">
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Popconfirm
            title="確定要刪除這個商品嗎？"
            description="此操作無法復原"
            onConfirm={() => handleDelete(record.itemID)}
            okText="確定"
            cancelText="取消"
            okType="danger"
          >
            <Tooltip title="刪除">
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <ResponsiveStyles />
      <Spin spinning={loading}>
        {/* 簡化頁面標題 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ marginBottom: '8px' }}>
            庫存品管理系統
          </Title>
          <p style={{ color: '#666', margin: 0, fontSize: '14px' }}>
            統一管理商品資訊、分類結構與價格設定
          </p>
        </div>

        {/* 響應式統計卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={12} lg={8}>
            <Card
              size={isMobile ? 'small' : 'default'}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <ShopOutlined style={{ color: '#52c41a' }} />
                  <span>商品狀態統計</span>
                </div>
              }
              className="unified-card"
              style={{ height: '100%' }}
              styles={{
                header: {
                  backgroundColor: '#fafafa',
                  borderBottom: '1px solid #f0f0f0',
                  padding: isMobile ? '12px 16px' : '16px 20px'
                },
                body: {
                  padding: isMobile ? '12px 16px' : '16px 20px'
                }
              }}
            >
              <div style={{ marginBottom: '16px' }}>
                <div style={{ fontSize: isMobile ? '20px' : '24px', fontWeight: 'bold', marginBottom: '12px' }}>
                  {stats.activeItems} / {stats.totalItems}
                </div>
                <Progress
                  percent={stats.totalItems > 0 ? Math.round((stats.activeItems / stats.totalItems) * 100) : 0}
                  strokeColor="#52c41a"
                  showInfo={false}
                  size="small"
                />
                <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                  啟用率: {stats.totalItems > 0 ? Math.round((stats.activeItems / stats.totalItems) * 100) : 0}%
                </div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={24} lg={16}>
            <Card
              size={isMobile ? 'small' : 'default'}
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <AppstoreOutlined style={{ color: '#1890ff' }} />
                  <span>分類與價格管理</span>
                </div>
              }
              className="unified-card"
              style={{ height: '100%' }}
              styles={{
                header: {
                  backgroundColor: '#fafafa',
                  borderBottom: '1px solid #f0f0f0',
                  padding: isMobile ? '12px 16px' : '16px 20px'
                },
                body: {
                  padding: isMobile ? '12px 16px' : '16px 20px'
                }
              }}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                    <Statistic
                      title="商品分類"
                      value={stats.totalCategories}
                      prefix={<FolderOutlined />}
                      valueStyle={{ fontSize: isMobile ? '18px' : '20px' }}
                    />
                  </div>
                  <Button
                    type="primary"
                    size={isMobile ? 'small' : 'small'}
                    icon={<SettingOutlined />}
                    onClick={() => setIsCategoryModalVisible(true)}
                    className="unified-button"
                    style={{ width: '100%' }}
                  >
                    管理分類
                  </Button>
                </Col>
                <Col xs={24} sm={12}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                    <Statistic
                      title="價格類型"
                      value={stats.totalPriceTypes}
                      prefix={<TagsOutlined />}
                      valueStyle={{ fontSize: isMobile ? '18px' : '20px' }}
                    />
                  </div>
                  <Button
                    type="primary"
                    size={isMobile ? 'small' : 'small'}
                    icon={<SettingOutlined />}
                    onClick={() => setIsPriceTypeModalVisible(true)}
                    className="unified-button"
                    style={{ width: '100%' }}
                  >
                    管理價格類型
                  </Button>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 動態搜尋和篩選 */}
        <FilterSearchContainer
          title="篩選與搜尋"
          filterOptions={itemFilterOptions}
          searchPlaceholder={isMobile ? "搜尋商品" : "搜尋商品名稱、編號或條碼"}
          showStats={true}
          stats={{
            total: data.items.length,
            filtered: filteredItems.length
          }}
          showClearMessage={true}
          clearMessage="已清除所有商品篩選條件"
          onFilterResult={(state) => {
            // 應用篩選邏輯
            const filtered = applyItemFilters(
              data.items,
              state.searchText,
              state.activeFilters,
              state.filterValues
            );
            setFilteredItems(filtered);
            setCurrentPage(1); // 重置分頁

            // 篩選結果已更新
          }}
          compact={isMobile}
          className="mb-6"
        />

        {/* 商品表格/列表 */}
        <Card
          title={
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
              width: '100%',
              flexWrap: isMobile ? 'wrap' : 'nowrap',
              gap: '12px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <UnorderedListOutlined />
                <span>商品列表</span>
                <Tag color="blue">{filteredItems.length} 項</Tag>
              </div>

              {/* 主要操作按鈕 - 左對齊到標題列 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                flexWrap: 'wrap',
                marginTop: isMobile ? '8px' : '0',
                marginLeft: isMobile ? '0' : '16px'
              }}>
                <Button
                  icon={<SyncOutlined />}
                  onClick={loadAllData}
                  loading={loading}
                  size="small"
                  title="重新載入"
                  className="unified-button"
                >
                  {!isMobile && '重新載入'}
                </Button>
                <Button
                  type="primary"
                  icon={<FileAddOutlined />}
                  onClick={showAddModal}
                  size="small"
                  className="unified-button"
                >
                  {isMobile ? '新增' : '新增商品'}
                </Button>
                {process.env.NODE_ENV === 'development' && (
                  <Button
                    danger
                    icon={<ExperimentOutlined />}
                    onClick={handleGenerateTestData}
                    loading={isGeneratingTestData}
                    disabled={loading}
                    size="small"
                    title="產生測試資料"
                    className="unified-button"
                  >
                    {isMobile ? '測試' : '產生測試資料'}
                  </Button>
                )}
              </div>
            </div>
          }
          className="enhanced-table unified-card"
          styles={{
            body: { padding: isMobile ? '8px' : '24px' },
            header: {
              padding: isMobile ? '12px 16px' : '16px 24px',
              borderBottom: '1px solid #f0f0f0',
              backgroundColor: '#fafafa'
            }
          }}
        >
          {isMobile ? (
            renderMobileList()
          ) : (
            <Table
              columns={columns}
              dataSource={filteredItems}
              rowKey="itemID"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize: pageSize,
                total: filteredItems.length,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => {
                  const isFiltered = filteredItems.length !== data.items.length;
                  return isFiltered
                    ? `第 ${range[0]}-${range[1]} 項，共 ${total} 項 (已篩選，原始資料 ${data.items.length} 項)`
                    : `第 ${range[0]}-${range[1]} 項，共 ${total} 項`;
                },
                pageSizeOptions: ['10', '20', '50', '100'],
                size: 'default',
                onChange: (page, size) => {
                  console.log(`📄 分頁變更: 第 ${page} 頁，每頁 ${size} 筆`);
                  setCurrentPage(page);
                  if (size !== pageSize) {
                    setPageSize(size);
                    setCurrentPage(1); // 改變每頁筆數時重置到第一頁
                  }
                },
                onShowSizeChange: (_, size) => {
                  console.log(`📊 每頁筆數變更: ${size} 筆`);
                  setPageSize(size);
                  setCurrentPage(1); // 改變每頁筆數時重置到第一頁
                }
              }}
              scroll={{ x: 1200 }}
              size="middle"
              rowClassName={(_, index) =>
                index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
              }
            />
          )}
        </Card>
      </Spin>

      {/* 商品編輯互動視窗 */}
      <ItemFormModal
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
          form.resetFields();
          setSelectedItem(null);
          setItemPrices({});
        }}
        selectedItem={selectedItem}
        onSubmit={handleSubmit}
        loading={loading}
        categories={data.categories}
        categoryTreeData={categoryTreeData}
        sortedCategoriesForDisplay={sortedCategoriesForDisplay}
        priceTypes={data.priceTypes}
        itemTaxTypes={itemTaxTypes}
        itemPrices={itemPrices}
        onItemPricesChange={setItemPrices}
        onCategoryDataChange={() => reloadData('categories')}
        onPriceTypeModalOpen={() => setIsPriceTypeModalVisible(true)}
      />

      {/* 分類管理組件 */}
      <ItemCategoryAdapter
        visible={isCategoryModalVisible}
        onClose={() => setIsCategoryModalVisible(false)}
        categories={data.categories}
        categoryTreeData={categoryTreeData}
        sortedCategoriesForDisplay={sortedCategoriesForDisplay}
        onDataChange={() => reloadData('categories')}
      />

      {/* 價格類型管理組件 */}
      <PriceTypeManagement
        visible={isPriceTypeModalVisible}
        onClose={() => setIsPriceTypeModalVisible(false)}
        priceTypes={data.priceTypes}
        onDataChange={() => reloadData('priceTypes')}
      />

      {/* 測試資料生成 Modal */}
      <Modal
        title="⚠️ 產生測試資料"
        open={isTestDataModalVisible}
        onOk={executeTestDataGeneration}
        onCancel={() => setIsTestDataModalVisible(false)}
        okText="確定產生"
        cancelText="取消"
        okType="danger"
        width={500}
        confirmLoading={isGeneratingTestData}
      >
        <div>
          <p>此操作將產生 <strong>{(testDataCount || 20000).toLocaleString()} 筆</strong> 測試商品資料。</p>
          <div style={{ margin: '16px 0' }}>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              自訂產生筆數：
            </label>
            <TestDataCountInput
              value={testDataCount}
              onChange={(value) => {
                console.log('🔄 測試資料數量變更:', value);
                setTestDataCount(value);
              }}
              min={1}
              max={50000}
              defaultValue={20000}
              placeholder="請輸入要產生的商品數量 (1-50,000)"
              showHint={true}
              onBlur={(value) => {
                console.log('👋 測試資料數量失去焦點:', value);
              }}
              onFocus={(value) => {
                console.log('👀 測試資料數量獲得焦點:', value);
              }}
            />
          </div>
          <p style={{ color: '#ff4d4f', marginBottom: 0 }}>
            ⚠️ 注意：這將在資料庫中新增大量測試資料，建議僅在開發環境中使用。
          </p>
        </div>
      </Modal>
    </div>
  );
}

export default ItemPage;