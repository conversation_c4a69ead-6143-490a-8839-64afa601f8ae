using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using FAST_ERP_Backend.Models;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Interfaces.Common;

namespace FAST_ERP_Backend.Services.Ims;

/// <summary> 測試資料產生服務 </summary>
public class TestDataService(ERPDbContext _context,ICurrentUserService _currentUserService,ILoggerService _logger)
{
    private readonly Random _random = new();
    /// <summary> 測試資料產生回應 </summary>
    public class TestDataGenerationResponse
    {
        public int CreatedCount { get; set; }
        public BatchInfo BatchInfo { get; set; } = new();
    }

    /// <summary> 批次處理資訊 </summary>
    public class BatchInfo
    {
        public int TotalBatches { get; set; }
        public int BatchSize { get; set; }
        public long ProcessingTime { get; set; }
    }

    /// <summary> 商品名稱模板 </summary>
    private readonly string[] _nameTemplates = {
        "電子產品", "日用品", "食品", "服飾", "家具", "文具", "運動用品",
        "美妝保養", "書籍", "玩具", "廚具", "清潔用品", "3C配件", "健康食品",
        "辦公用品", "園藝用品", "汽車用品", "寵物用品", "嬰幼兒用品", "工具"
    };

    /// <summary> 單位選項 </summary>
    private readonly string[] _units = { "個", "公斤", "公升", "盒", "包", "組", "套", "張", "支", "瓶" };

    /// <summary> 分類價格範圍對應 </summary>
    private readonly Dictionary<string, (decimal min, decimal max)> _categoryPriceRanges = new()
    {
        { "電子產品", (500m, 50000m) },
        { "3C配件", (100m, 5000m) },
        { "家具", (1000m, 100000m) },
        { "服飾", (200m, 5000m) },
        { "美妝保養", (50m, 2000m) },
        { "書籍", (100m, 1000m) },
        { "玩具", (50m, 3000m) },
        { "運動用品", (200m, 10000m) },
        { "日用品", (10m, 500m) },
        { "食品", (5m, 1000m) },
        { "文具", (5m, 200m) },
        { "廚具", (50m, 5000m) },
        { "清潔用品", (20m, 300m) },
        { "健康食品", (100m, 3000m) },
        { "辦公用品", (10m, 1000m) },
        { "園藝用品", (50m, 2000m) },
        { "汽車用品", (100m, 10000m) },
        { "寵物用品", (20m, 1000m) },
        { "嬰幼兒用品", (50m, 3000m) },
        { "工具", (100m, 20000m) }
    };

    /// <summary> 產生唯一商品編號 </summary>
    private async Task<string> GenerateUniqueItemNumber()
    {
        var existingNumbers = await _context.Ims_Item
            .Where(i => !i.IsDeleted)
            .Select(i => i.CustomNO)
            .ToHashSetAsync();

        string itemNumber;
        int attempts = 0;
        const int maxAttempts = 1000;

        do
        {
            var randomNumber = _random.Next(1, 999999).ToString("D6");
            itemNumber = $"ITEM-{randomNumber}";
            attempts++;
        }
        while (existingNumbers.Contains(itemNumber) && attempts < maxAttempts);

        if (attempts >= maxAttempts)
        {
            // 使用時間戳記確保唯一性
            var timestamp = DateTimeOffset.Now.ToUnixTimeSeconds().ToString().Substring(5);
            itemNumber = $"ITEM-{timestamp}";
        }

        return itemNumber;
    }

    /// <summary> 產生 EAN-13 條碼 </summary>
    private string GenerateEAN13Barcode()
    {
        // 產生前12位數字
        var barcode = "";
        for (int i = 0; i < 12; i++)
        {
            barcode += _random.Next(0, 10).ToString();
        }

        // 計算校驗位
        var sum = 0;
        for (int i = 0; i < 12; i++)
        {
            var digit = int.Parse(barcode[i].ToString());
            sum += (i % 2 == 0) ? digit : digit * 3;
        }

        var checkDigit = (10 - (sum % 10)) % 10;
        return barcode + checkDigit.ToString();
    }

    /// <summary> 根據分類名稱取得價格範圍 </summary>
    private (decimal min, decimal max) GetPriceRangeByCategory(string categoryName)
    {
        foreach (var template in _nameTemplates)
        {
            if (categoryName.Contains(template) && _categoryPriceRanges.ContainsKey(template))
            {
                return _categoryPriceRanges[template];
            }
        }
        
        // 預設價格範圍
        return (50m, 1000m);
    }

    /// <summary> 產生隨機價格 </summary>
    private decimal GenerateRandomPrice(decimal min, decimal max)
    {
        var range = max - min;
        var randomValue = (decimal)_random.NextDouble() * range + min;
        
        // 四捨五入到最接近的整數
        return Math.Round(randomValue, 0);
    }

    /// <summary> 產生隨機商品資料 </summary>
    private async Task<Item> GenerateRandomItemData(List<ItemCategory> categories)
    {
        // 隨機選擇名稱模板和編號
        var template = _nameTemplates[_random.Next(_nameTemplates.Length)];
        var randomId = _random.Next(1, 10000);
        var name = $"{template}-{randomId:D4}";

        // 產生唯一商品編號
        var customNO = await GenerateUniqueItemNumber();

        // 隨機選擇分類
        var category = categories.Count > 0 ? categories[_random.Next(categories.Count)] : null;

        // 隨機選擇單位
        var unit = _units[_random.Next(_units.Length)];

        // 70% 機率為啟用狀態
        var isStop = _random.NextDouble() > 0.7;

        // 30% 機率有描述
        var description = _random.NextDouble() < 0.3
            ? $"測試商品 - 自動產生於 {DateTime.Now:yyyy/MM/dd HH:mm:ss}"
            : string.Empty;

        // 產生國際條碼
        var internationalBarCode = GenerateEAN13Barcode();

        var item = new Item
        {
            ItemID = Guid.NewGuid(),
            Name = name,
            CustomNO = customNO,
            InternationalBarCode = internationalBarCode,
            Unit = unit,
            ItemCategoryID = category?.ItemCategoryID,
            Description = description,
            IsStop = isStop,
            TaxType = _random.Next(0, 10) switch
            {
                < 7 => ItemTaxType.Taxable,   // 70% 應稅
                < 9 => ItemTaxType.TaxFree,   // 20% 免稅
                _ => ItemTaxType.ZeroRate     // 10% 零稅率
            },
            SortCode = 0,
            CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
            CreateUserId = _currentUserService.UserId
        };

        return item;
    }

    /// <summary> 為商品產生價格資料 </summary>
    private async Task<List<ItemPrice>> GenerateItemPrices(Item item, List<PriceType> priceTypes, string? categoryName)
    {
        var prices = new List<ItemPrice>();
        
        // 取得價格範圍
        var (minPrice, maxPrice) = GetPriceRangeByCategory(categoryName ?? "預設");

        foreach (var priceType in priceTypes.Take(3)) // 最多產生3種價格類型
        {
            var basePrice = GenerateRandomPrice(minPrice, maxPrice);
            
            // 根據價格類型調整價格
            var adjustedPrice = priceType.Name switch
            {
                "零售價" or "一般價" => basePrice,
                "會員價" => basePrice * 0.9m, // 9折
                "批發價" or "大批價" => basePrice * 0.8m, // 8折
                "特價" => basePrice * 0.7m, // 7折
                _ => basePrice
            };

            var itemPrice = new ItemPrice
            {
                ItemPriceID = Guid.NewGuid(),
                ItemID = item.ItemID,
                PriceTypeID = priceType.PriceTypeID,
                Price = Math.Round(adjustedPrice, 0),
                CreateTime = DateTimeOffset.Now.ToUnixTimeSeconds(),
                CreateUserId = _currentUserService.UserId
            };

            prices.Add(itemPrice);
        }

        return prices;
    }

    /// <summary> 批次新增商品和價格 </summary>
    private async Task<int> BatchAddItemsWithPrices(List<Item> items, List<ItemCategory> categories, List<PriceType> priceTypes)
    {
        var allPrices = new List<ItemPrice>();

        foreach (var item in items)
        {
            // 找到對應的分類名稱
            var category = categories.FirstOrDefault(c => c.ItemCategoryID == item.ItemCategoryID);
            var categoryName = category?.Name;

            // 產生價格資料
            var itemPrices = await GenerateItemPrices(item, priceTypes, categoryName);
            allPrices.AddRange(itemPrices);
        }

        // 批次新增商品
        await _context.Ims_Item.AddRangeAsync(items);
        
        // 批次新增價格
        await _context.Ims_ItemPrice.AddRangeAsync(allPrices);
        
        // 儲存變更
        await _context.SaveChangesAsync();

        return items.Count;
    }

    /// <summary> 產生大量測試商品資料 </summary>
    public async Task<TestDataGenerationResponse> GenerateTestItems(int count = 20000)
    {
        var startTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
        
        try
        {
            await _logger.LogInfoAsync(
                $"開始產生測試商品資料 - 數量: {count} 筆",
                "TestDataService"
            );

            // 載入現有分類和價格類型
            var categories = await _context.Ims_ItemCategory
                .Where(c => !c.IsDeleted)
                .ToListAsync();

            var priceTypes = await _context.Ims_PriceType
                .Where(p => !p.IsDeleted && !p.IsStop)
                .OrderBy(p => p.SortCode)
                .ToListAsync();

            if (!priceTypes.Any())
            {
                throw new InvalidOperationException("系統中沒有可用的價格類型，請先建立價格類型");
            }

            // 批次設定
            const int batchSize = 500;
            var totalBatches = (int)Math.Ceiling((double)count / batchSize);
            var totalCreated = 0;

            // 分批處理
            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++)
            {
                var currentBatchSize = Math.Min(batchSize, count - batchIndex * batchSize);
                var batchItems = new List<Item>();

                // 產生當前批次的商品資料
                for (int i = 0; i < currentBatchSize; i++)
                {
                    var item = await GenerateRandomItemData(categories);
                    batchItems.Add(item);
                }

                // 批次新增商品和價格
                var createdCount = await BatchAddItemsWithPrices(batchItems, categories, priceTypes);
                totalCreated += createdCount;

                await _logger.LogInfoAsync(
                    $"批次處理完成 - 第 {batchIndex + 1}/{totalBatches} 批次，新增 {createdCount} 筆商品",
                    "TestDataService"
                );
            }

            var endTime = DateTimeOffset.Now.ToUnixTimeMilliseconds();
            var processingTime = endTime - startTime;

            await _logger.LogInfoAsync(
                $"測試資料產生完成 - 總計: {totalCreated} 筆，耗時: {processingTime}ms",
                "TestDataService"
            );

            return new TestDataGenerationResponse
            {
                CreatedCount = totalCreated,
                BatchInfo = new BatchInfo
                {
                    TotalBatches = totalBatches,
                    BatchSize = batchSize,
                    ProcessingTime = processingTime
                }
            };
        }
        catch (Exception ex)
        {
            await _logger.LogErrorAsync(
                $"產生測試資料時發生錯誤 - 請求數量: {count} 筆",
                ex,
                "TestDataService"
            );
            throw;
        }
    }
}