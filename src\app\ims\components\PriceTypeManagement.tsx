"use client";

import React, { useState, useEffect, useRef } from 'react';
import { Modal, Form, Input, Button, Space, Tag, Row, Col, Switch, Popconfirm, message, Card, Badge, Alert } from 'antd';
import type { InputRef } from 'antd';
import { PlusOutlined, EditOutlined, SaveOutlined, UndoOutlined, TagsOutlined, DeleteOutlined } from '@ant-design/icons';
import { PriceType } from '@/services/ims/ItemService';
import { addPriceType, editPriceType, deletePriceType } from '@/services/ims/PriceTypeService';
import ResponsiveStyles from './shared/ResponsiveStyles';

interface PriceTypeManagementProps {
  visible: boolean;
  onClose: () => void;
  priceTypes: PriceType[];
  onDataChange: () => void; // 回調函數，用於通知父組件重新載入資料
}

const PriceTypeManagement: React.FC<PriceTypeManagementProps> = ({
  visible,
  onClose,
  priceTypes,
  onDataChange
}) => {
  // 狀態管理
  const [selectedPriceType, setSelectedPriceType] = useState<PriceType | null>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [operationMode, setOperationMode] = useState<'add' | 'edit'>('add');

  // Focus management refs
  const nameInputRef = useRef<InputRef>(null);
  const formContainerRef = useRef<HTMLDivElement>(null);

  // 移動端檢測狀態
  const [isMobile, setIsMobile] = useState(false);

  // 滾動到輸入框的函數
  const scrollToInput = () => {
    setTimeout(() => {
      if (nameInputRef.current && formContainerRef.current) {
        // 先設置焦點
        nameInputRef.current.focus();

        // 然後滾動到輸入框
        const inputElement = nameInputRef.current.input;
        if (inputElement) {
          inputElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
          });
        }
      }
    }, 150);
  };

  // 重置表單為新增模式
  const handleAddNew = () => {
    setSelectedPriceType(null);
    setOperationMode('add');
    form.resetFields();
    // 確保新增時 allowStop 預設為 true，isStop 預設為 false
    form.setFieldsValue({
      allowStop: true,
      isStop: false
    });
  };

  // 處理表單提交
  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      // 修正邏輯：新增時強制設定 allowStop: true，編輯時保持原有值
      const submitData = selectedPriceType
        ? { ...selectedPriceType, ...values } // 編輯時保持原有的 allowStop 值
        : { ...values, allowStop: true }; // 新增時強制設為允許停用

      console.log('🔄 提交價格類型資料:', submitData);

      const response = selectedPriceType
        ? await editPriceType(submitData)
        : await addPriceType(submitData);

      if (response.success) {
        message.success(selectedPriceType ? '價格類型更新成功' : '價格類型新增成功');
        // 重置表單以便繼續新增
        handleAddNew();
        // 通知父組件重新載入資料
        onDataChange();
        console.log('✅ 價格類型操作成功');
      } else {
        message.error(response.message || '操作失敗');
      }
    } catch (error) {
      console.error('❌ 價格類型操作失敗:', error);
      message.error('操作失敗，請重試');
    } finally {
      setLoading(false);
    }
  };

  // 處理刪除
  const handleDelete = async (priceTypeId: string) => {
    try {
      console.log('🔄 刪除價格類型:', priceTypeId);
      const response = await deletePriceType(priceTypeId);
      if (response.success) {
        message.success('價格類型刪除成功');
        onDataChange();
        console.log('✅ 價格類型刪除成功');
      } else {
        message.error(response.message || '刪除失敗');
      }
    } catch (error) {
      console.error('❌ 刪除價格類型失敗:', error);
      message.error('刪除失敗，請重試');
    }
  };

  // 處理編輯
  const handleEdit = (priceType: PriceType) => {
    setSelectedPriceType(priceType);
    setOperationMode('edit');
    form.setFieldsValue(priceType);

    // 設置焦點並滾動到輸入框
    setTimeout(() => {
      if (nameInputRef.current) {
        nameInputRef.current.focus();
        nameInputRef.current.select();
        scrollToInput();
      }
    }, 100);
  };

  // 當互動視窗打開時，重置表單並設置焦點
  useEffect(() => {
    if (visible) {
      handleAddNew();
      // 延遲設置焦點並滾動，確保互動視窗完全渲染
      scrollToInput();
    }
  }, [visible]);

  // 當操作模式改變時設置焦點
  useEffect(() => {
    if (visible && (operationMode === 'edit' || operationMode === 'add')) {
      setTimeout(() => {
        if (nameInputRef.current) {
          nameInputRef.current.focus();
          // 如果是編輯模式，選中所有文字
          if (operationMode === 'edit') {
            nameInputRef.current.select();
          }
          scrollToInput();
        }
      }, 100);
    }
  }, [operationMode, visible]);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);

  // 操作模式配置
  const modeConfig = {
    add: {
      title: '新增價格類型',
      color: '#52c41a',
      icon: <PlusOutlined />,
      buttonText: '新增',
      cardTitle: '新增模式',
      description: '建立新的價格類型'
    },
    edit: {
      title: '編輯價格類型',
      color: '#1890ff',
      icon: <EditOutlined />,
      buttonText: '更新',
      cardTitle: '編輯模式',
      description: `正在編輯：${selectedPriceType?.name || ''}`
    }
  };

  const currentMode = modeConfig[operationMode];

  return (
    <>
      <ResponsiveStyles />
      <Modal
        title={
          <Space>
            <TagsOutlined style={{ color: '#1890ff' }} />
            <span>價格類型管理</span>
          </Space>
        }
        open={visible}
        onCancel={() => {
          handleAddNew();
          onClose();
        }}
        footer={null}
        width={isMobile ? '95%' : 1000}
        style={isMobile ? { top: 20 } : { top: 20 }}
        className="unified-card"
      >
        <Row gutter={isMobile ? [8, 16] : [16, 16]} style={{ height: isMobile ? 'auto' : '70vh' }}>
          {/* 左側：價格類型列表 */}
          <Col span={isMobile ? 24 : 14} style={{ marginBottom: isMobile ? 16 : 0 }}>
            <Card
              title={
                <Space>
                  <TagsOutlined style={{ color: '#52c41a' }} />
                  <span>價格類型列表</span>
                  <Badge count={priceTypes.length} style={{ backgroundColor: '#52c41a' }} />
                </Space>
              }
              className="unified-card"
              style={{ height: isMobile ? 'auto' : '100%' }}
              styles={{
                body: {
                  padding: isMobile ? '12px' : '16px',
                  height: isMobile ? 'auto' : 'calc(100% - 57px)',
                  overflow: 'auto'
                }
              }}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {priceTypes.map((priceType) => (
                  <div
                    key={priceType.priceTypeID}
                    style={{
                      padding: '12px',
                      border: selectedPriceType?.priceTypeID === priceType.priceTypeID
                        ? '2px solid #1890ff'
                        : '1px solid #e8e8e8',
                      borderRadius: '6px',
                      backgroundColor: selectedPriceType?.priceTypeID === priceType.priceTypeID
                        ? '#f0f9ff'
                        : '#fafafa',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onClick={() => handleEdit(priceType)}
                  >
                    <Row justify="space-between" align="middle">
                      <Col flex="auto">
                        <div style={{ marginBottom: '8px' }}>
                          <div style={{
                            fontWeight: 600,
                            fontSize: '14px',
                            color: '#262626',
                            marginBottom: '4px'
                          }}>
                            {priceType.name}
                          </div>
                          {priceType.description && (
                            <div style={{
                              fontSize: '12px',
                              color: '#8c8c8c',
                              lineHeight: '1.4'
                            }}>
                              {priceType.description}
                            </div>
                          )}
                        </div>

                        <Space size="small" wrap>
                          <Tag
                            color={priceType.isStop ? 'red' : 'green'}
                            className="unified-tag"
                            style={{ fontSize: '11px' }}
                          >
                            {priceType.isStop ? '停用' : '啟用'}
                          </Tag>
                          {priceType.sortCode && (
                            <Tag
                              color="purple"
                              className="unified-tag"
                              style={{ fontSize: '11px' }}
                            >
                              排序: {priceType.sortCode}
                            </Tag>
                          )}
                        </Space>
                      </Col>

                      <Col>
                        <Space size="small">
                          <Button
                            type="text"
                            size="small"
                            icon={<EditOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(priceType);
                            }}
                            className="unified-button"
                            title="編輯"
                          />
                          <Popconfirm
                            title="確定要刪除此價格類型嗎？"
                            description="刪除後無法復原，請確認。"
                            onConfirm={(e) => {
                              e?.stopPropagation();
                              handleDelete(priceType.priceTypeID);
                            }}
                            okText="確定"
                            cancelText="取消"
                          >
                            <Button
                              type="text"
                              size="small"
                              icon={<DeleteOutlined />}
                              danger
                              onClick={(e) => e.stopPropagation()}
                              className="unified-button"
                              title="刪除"
                            />
                          </Popconfirm>
                        </Space>
                      </Col>
                    </Row>
                  </div>
              ))}

                {priceTypes.length === 0 && (
                  <div style={{
                    textAlign: 'center',
                    padding: '40px 20px',
                    color: '#8c8c8c'
                  }}>
                    <TagsOutlined style={{ fontSize: '32px', color: '#d9d9d9', marginBottom: '12px' }} />
                    <div style={{ fontSize: '14px' }}>尚無價格類型</div>
                    <div style={{ fontSize: '12px', marginTop: '4px' }}>
                      請使用右側表單新增價格類型
                    </div>
                  </div>
                )}
              </div>
            </Card>
          </Col>

          {/* 右側：表單區域 */}
          <Col span={isMobile ? 24 : 10}>
            <div ref={formContainerRef}>
              <Card
                title={
                  <Space>
                    {currentMode.icon}
                    <span>{currentMode.cardTitle}</span>
                  </Space>
                }
                extra={
                  <Button
                    type="text"
                    size="small"
                    icon={<UndoOutlined />}
                    onClick={handleAddNew}
                    className="unified-button"
                    title="重置為新增模式"
                  >
                    {!isMobile && '重置'}
                  </Button>
                }
                className="unified-card"
                style={{ height: isMobile ? 'auto' : '100%' }}
                styles={{
                  body: {
                    padding: isMobile ? '12px' : '16px',
                    height: isMobile ? 'auto' : 'calc(100% - 57px)',
                    overflow: 'auto'
                  }
                }}
              >

                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSubmit}
                >
                  <Form.Item
                    label="價格類型名稱"
                    name="name"
                    rules={[{ required: true, message: '請輸入價格類型名稱' }]}
                  >
                    <Input
                      ref={nameInputRef}
                      placeholder="請輸入價格類型名稱"
                      className="unified-input"
                      autoFocus={true}
                    />
                  </Form.Item>

                  <Form.Item
                    label="描述"
                    name="description"
                  >
                    <Input.TextArea
                      rows={3}
                      placeholder="請輸入價格類型描述"
                      className="unified-input"
                    />
                  </Form.Item>

                  <Row gutter={isMobile ? 8 : 16}>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        label="排序碼"
                        name="sortCode"
                      >
                        <Input
                          type="number"
                          placeholder="請輸入排序碼"
                          className="unified-input"
                        />
                      </Form.Item>
                    </Col>
                    <Col xs={24} sm={12}>
                      <Form.Item
                        label="狀態"
                        name="isStop"
                        getValueFromEvent={(checked) => checked}
                        getValueProps={(value) => ({ checked: value })}
                        valuePropName="checked"
                      >
                        <Switch
                          checkedChildren="停用"
                          unCheckedChildren="啟用"
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <div style={{
                    display: 'flex',
                    gap: '8px',
                    marginTop: '16px',
                    flexDirection: isMobile ? 'column' : 'row'
                  }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      icon={operationMode === 'add' ? <PlusOutlined /> : <SaveOutlined />}
                      className="unified-button"
                      style={{ flex: 1 }}
                    >
                      {currentMode.buttonText}
                    </Button>
                    <Button
                      type="default"
                      onClick={() => {
                        handleAddNew();
                        onClose();
                      }}
                      className="unified-button"
                      style={{ flex: isMobile ? 1 : 'none' }}
                    >
                      關閉
                    </Button>
                  </div>
                </Form>
              </Card>
            </div>
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default PriceTypeManagement;
