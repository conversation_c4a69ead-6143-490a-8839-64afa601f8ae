"use client";

import React from 'react';

/**
 * 共享響應式樣式組件
 *
 * 提供統一的響應式設計樣式，確保所有 IMS 模組具有一致的視覺效果和響應式行為。
 * 遵循 FastERP 設計系統規範，支援移動端 (≤768px)、平板 (769-1024px) 和桌面 (>1024px) 斷點。
 *
 * 使用方式：
 * ```tsx
 * import { ResponsiveStyles } from '@/app/ims/components/shared/ResponsiveStyles';
 *
 * const MyComponent = () => (
 *   <div>
 *     <ResponsiveStyles />
 *     {/* 你的組件內容 */}
 *   </div>
 * );
 * ```
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
const ResponsiveStyles: React.FC = () => {
  React.useEffect(() => {
    // 動態插入樣式到 head
    const styleId = 'fastERP-responsive-styles';
    if (document.getElementById(styleId)) {
      return; // 樣式已存在
    }

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      /* ===== 篩選和搜尋組件樣式 ===== */
      .filter-select-active .ant-select-selector {
        border-color: #1890ff !important;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
      }
      
      .filter-control {
        position: relative;
      }
      
      .filter-control:hover {
        z-index: 1;
      }

      /* ===== 卡片組件統一樣式 ===== */
      .ant-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: box-shadow 0.2s ease;
      }
      
      .ant-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }

      /* ===== 表格增強樣式 ===== */
      .enhanced-table .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        border-bottom: 2px solid #f0f0f0;
      }
      
      .enhanced-table .ant-table-tbody > tr:hover > td {
        background: #f0f9ff;
      }

      /* ===== 統一設計系統樣式 ===== */
      .unified-card {
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
        border: 1px solid #e8e8e8 !important;
      }

      .unified-card .ant-card-head {
        background-color: #fafafa !important;
        border-bottom: 1px solid #f0f0f0 !important;
      }

      .unified-button {
        border-radius: 6px !important;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
      }

      .unified-button:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.15) !important;
      }

      .unified-input {
        border-radius: 6px !important;
        box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
        transition: all 0.2s ease !important;
      }

      .unified-input:focus {
        box-shadow: 0 2px 4px rgba(24,144,255,0.2) !important;
      }

      .unified-tag {
        border-radius: 4px !important;
        font-weight: 500 !important;
      }

      /* ===== 移動端列表樣式 ===== */
      .mobile-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .mobile-item {
        border-radius: 8px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06) !important;
        border: 1px solid #e8e8e8 !important;
        transition: box-shadow 0.2s ease;
      }

      .mobile-item:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.12) !important;
      }

      .mobile-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
      }

      .mobile-title {
        flex: 1;
      }

      .mobile-content {
        margin-top: 12px;
      }

      /* ===== 表格行樣式 ===== */
      .table-row-light {
        background-color: #fafafa;
      }

      .table-row-dark {
        background-color: #ffffff;
      }

      /* ===== 響應式設計 - 移動端 (≤768px) ===== */
      @media (max-width: 768px) {
        /* Modal 全屏顯示 */
        .ant-modal {
          margin: 0;
          max-width: 100vw;
          top: 0;
          padding: 0;
        }
        
        .ant-modal-content {
          border-radius: 0;
          height: 100vh;
          display: flex;
          flex-direction: column;
        }

        .ant-modal-body {
          flex: 1;
          overflow-y: auto;
          padding: 16px;
        }

        /* 表單項目間距調整 */
        .ant-form-item {
          margin-bottom: 16px;
        }
        
        .ant-col {
          margin-bottom: 8px;
        }

        /* 按鈕組響應式 */
        .ant-space {
          flex-wrap: wrap;
          width: 100%;
        }
        
        .ant-space-item {
          margin-bottom: 8px;
        }

        /* 篩選控制項全寬 */
        .filter-control {
          width: 100%;
          min-width: 200px;
        }

        /* 卡片內邊距調整 */
        .ant-card-body {
          padding: 12px;
        }

        .ant-card-head {
          padding: 8px 12px;
        }

        /* 統計卡片響應式 */
        .ant-statistic-title {
          font-size: 12px;
        }

        .ant-statistic-content {
          font-size: 18px;
        }

        /* 表格滾動優化 */
        .ant-table-wrapper {
          overflow-x: auto;
        }

        /* 標籤頁響應式 */
        .ant-tabs-tab {
          padding: 8px 12px;
          font-size: 12px;
        }

        .ant-tabs-content-holder {
          overflow-x: auto;
        }
      }

      /* ===== 響應式設計 - 小屏幕 (≤576px) ===== */
      @media (max-width: 576px) {
        /* 更緊湊的間距 */
        .ant-space {
          gap: 4px !important;
        }

        /* 按鈕尺寸調整 */
        .ant-btn {
          font-size: 12px;
          padding: 4px 8px;
          height: auto;
        }

        /* 標籤尺寸調整 */
        .ant-tag {
          font-size: 11px;
          padding: 2px 6px;
        }

        /* 輸入框調整 */
        .ant-input {
          font-size: 14px;
        }

        /* 選擇器調整 */
        .ant-select {
          font-size: 14px;
        }
      }

      /* ===== 響應式設計 - 平板 (769px-1024px) ===== */
      @media (min-width: 769px) and (max-width: 1024px) {
        /* 平板優化的間距 */
        .ant-card-body {
          padding: 20px;
        }

        /* 表格列寬優化 */
        .ant-table-thead > tr > th {
          padding: 12px 8px;
        }

        .ant-table-tbody > tr > td {
          padding: 12px 8px;
        }
      }

      /* ===== 響應式設計 - 桌面 (>1024px) ===== */
      @media (min-width: 1025px) {
        /* 桌面優化的間距和佈局 */
        .ant-card-body {
          padding: 24px;
        }

        /* 更寬鬆的表格間距 */
        .ant-table-thead > tr > th {
          padding: 16px;
        }

        .ant-table-tbody > tr > td {
          padding: 16px;
        }

        /* 更大的按鈕間距 */
        .ant-space {
          gap: 8px;
        }
      }

      /* ===== 防止元素重疊和溢出 ===== */
      .ant-form-item-label {
        padding-bottom: 4px;
      }
      
      .ant-form-item-control {
        min-height: 32px;
      }
      
      .ant-modal-body {
        max-height: 70vh;
        overflow-y: auto;
      }

      /* ===== 容器溢出防護 ===== */
      .container-overflow-protection {
        max-width: 100%;
        overflow-x: auto;
      }

      /* ===== 文字溢出處理 ===== */
      .text-ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      /* ===== 圖片響應式 ===== */
      .responsive-image {
        max-width: 100%;
        height: auto;
      }

      /* ===== 無障礙設計增強 ===== */
      .ant-btn:focus,
      .ant-input:focus,
      .ant-select:focus {
        outline: 2px solid #1890ff;
        outline-offset: 2px;
      }

      /* ===== 打印樣式 ===== */
      @media print {
        .ant-btn,
        .ant-pagination,
        .filter-control {
          display: none !important;
        }
        
        .ant-card {
          box-shadow: none !important;
          border: 1px solid #ccc !important;
        }
      }
    `;

    document.head.appendChild(style);

    // 清理函數
    return () => {
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);

  return null; // 這個組件不渲染任何內容
};

export { ResponsiveStyles };
export default ResponsiveStyles;
